{"name": "mern-blog-backend", "version": "1.0.0", "description": "Backend for MERN blogging platform", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"bcryptjs": "^2.4.3", "cloudinary": "^1.41.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^7.1.0", "hpp": "^0.2.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "rate-limit-mongo": "^2.3.2", "socket.io": "^4.8.1", "uuid": "^9.0.1", "xss-clean": "^0.1.4"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "keywords": ["express", "mongodb", "blog", "api"], "author": "", "license": "MIT"}