@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-secondary-900 bg-white;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
  
  /* Dark mode styles */
  .dark body {
    @apply text-secondary-100 bg-secondary-900;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary-100;
  }
  
  .dark ::-webkit-scrollbar-track {
    @apply bg-secondary-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 rounded-full;
  }
  
  .dark ::-webkit-scrollbar-thumb {
    @apply bg-secondary-600;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400;
  }
  
  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-500;
  }
}

@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-200 text-secondary-900 hover:bg-secondary-300 focus:ring-secondary-500;
  }
  
  .dark .btn-secondary {
    @apply bg-secondary-700 text-secondary-100 hover:bg-secondary-600;
  }
  
  .btn-outline {
    @apply btn border border-secondary-300 text-secondary-700 hover:bg-secondary-50 focus:ring-secondary-500;
  }
  
  .dark .btn-outline {
    @apply border-secondary-600 text-secondary-300 hover:bg-secondary-800;
  }
  
  .btn-ghost {
    @apply btn text-secondary-700 hover:bg-secondary-100 focus:ring-secondary-500;
  }
  
  .dark .btn-ghost {
    @apply text-secondary-300 hover:bg-secondary-800;
  }
  
  .btn-danger {
    @apply btn bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
  }
  
  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 border border-secondary-300 rounded-lg shadow-sm placeholder-secondary-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .dark .input {
    @apply bg-secondary-800 border-secondary-600 text-secondary-100 placeholder-secondary-500;
  }
  
  .input-error {
    @apply input border-red-300 focus:ring-red-500 focus:border-red-500;
  }
  
  /* Card styles */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-secondary-200 overflow-hidden;
  }
  
  .dark .card {
    @apply bg-secondary-800 border-secondary-700;
  }
  
  .card-hover {
    @apply card transition-all duration-200 hover:shadow-md hover:-translate-y-1;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-secondary-200 border-t-primary-600;
  }
  
  /* Text styles */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }
  
  /* Focus styles for accessibility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .dark .focus-ring {
    @apply focus:ring-offset-secondary-900;
  }

  /* Enhanced prose styles for blog content */
  .prose {
    @apply text-secondary-800;
  }

  .dark .prose {
    @apply text-secondary-200;
  }

  .prose p {
    @apply my-4 text-secondary-800 leading-relaxed;
  }

  .dark .prose p {
    @apply text-secondary-200;
  }

  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    @apply text-secondary-900 font-bold;
  }

  .dark .prose h1, .dark .prose h2, .dark .prose h3, .dark .prose h4, .dark .prose h5, .dark .prose h6 {
    @apply text-white;
  }

  .prose ul, .prose ol {
    @apply my-4 pl-6;
  }

  .prose li {
    @apply my-1 text-secondary-800;
  }

  .dark .prose li {
    @apply text-secondary-200;
  }

  .prose img {
    @apply rounded-lg my-6 shadow-md;
  }

  .prose pre {
    @apply bg-secondary-100 dark:bg-secondary-800 p-4 rounded-lg overflow-x-auto my-4;
  }

  .prose code {
    @apply bg-secondary-100 dark:bg-secondary-800 px-1 py-0.5 rounded text-secondary-800 dark:text-secondary-200;
  }

  .prose blockquote {
    @apply border-l-4 border-primary-500 pl-4 italic my-4 text-secondary-700 dark:text-secondary-300;
  }

  .prose a {
    @apply text-primary-600 dark:text-primary-400 hover:underline;
  }

  .prose table {
    @apply w-full my-6 border-collapse;
  }

  .prose th {
    @apply bg-secondary-100 dark:bg-secondary-800 text-secondary-900 dark:text-white p-2 border border-secondary-300 dark:border-secondary-700;
  }

  .prose td {
    @apply p-2 border border-secondary-300 dark:border-secondary-700;
  }

  .prose hr {
    @apply my-8 border-secondary-300 dark:border-secondary-700;
  }
}

@layer utilities {
  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
  
  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }
  
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
  
  /* Layout utilities */
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .container-narrow {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  /* Gradient utilities */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-700;
  }
  
  .gradient-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-700;
  }
  
  .gradient-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-700;
  }
}

/* Navigation link styles */
.nav-link {
  @apply text-secondary-700 dark:text-secondary-300 hover:text-primary-600 dark:hover:text-primary-400 transition-colors;
}
