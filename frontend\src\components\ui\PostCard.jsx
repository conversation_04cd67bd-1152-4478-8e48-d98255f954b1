import { Link, useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { <PERSON><PERSON><PERSON>, HiHeart, HiClock, HiCalendar, HiTag, HiOutlineHeart, HiBookmark, HiOutlineBookmark, HiPencil, HiTrash } from 'react-icons/hi'
import { formatDate, formatNumber, truncateText } from '@utils/helpers'
import { DATE_FORMATS } from '@utils/constants'
import { useState, useEffect } from 'react'
import { useSelector, useDispatch } from 'react-redux'
import toast from 'react-hot-toast'
import { deletePost } from '@store/slices/postsSlice'

const PostCard = ({
  post,
  showAuthor = true,
  className = '',
  variant = 'default', // 'default', 'compact', 'featured'
  onLike = null,
  onSave = null,
  showActions = false
}) => {
  const { user, isAuthenticated } = useSelector((state) => state.auth)
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const [isLiked, setIsLiked] = useState(post?.isLiked || false)
  const [isSaved, setIsSaved] = useState(post?.isSaved || false)
  const [likeCount, setLikeCount] = useState(post?.likes?.length || post?.likeCount || 0)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isAuthor, setIsAuthor] = useState(false)

  const {
    _id,
    title,
    slug,
    excerpt,
    featuredImage,
    author,
    publishedAt,
    views,
    category,
    tags = [],
    readTime,
    status
  } = post

  // Determine if current user is the author of the post
  useEffect(() => {
    // Default to false
    let authorMatch = false;
    
    // Only check if user is authenticated and both user and author exist
    if (isAuthenticated && user && author) {
      // Get user ID as string
      const userId = String(user._id);
      
      // Get author ID as string based on whether author is an object or string
      let authorId;
      if (typeof author === 'object' && author._id) {
        authorId = String(author._id);
      } else if (typeof author === 'string') {
        authorId = String(author);
      }
      
      // Check if IDs match
      if (userId && authorId && userId === authorId) {
        authorMatch = true;
      }
      
      // Force debug logging
      console.log('AUTHOR CHECK:', {
        userId,
        authorId,
        isMatch: authorMatch,
        user,
        author,
        postTitle: title
      });
    }
    
    // Set the state
    setIsAuthor(authorMatch);
  }, [isAuthenticated, user, author, title]);

  // Handle like action
  const handleLike = async (e) => {
    e.preventDefault()
    e.stopPropagation()

    if (!isAuthenticated) {
      toast.error('Please log in to like posts')
      return
    }

    try {
      setIsLiked(!isLiked)
      setLikeCount(prev => isLiked ? prev - 1 : prev + 1)

      if (onLike) {
        await onLike(_id)
      }
    } catch (error) {
      // Revert optimistic update on error
      setIsLiked(isLiked)
      setLikeCount(prev => isLiked ? prev + 1 : prev - 1)
      toast.error('Failed to like post')
    }
  }

  // Handle save action
  const handleSave = async (e) => {
    e.preventDefault()
    e.stopPropagation()

    if (!isAuthenticated) {
      toast.error('Please log in to save posts')
      return
    }

    try {
      setIsSaved(!isSaved)

      if (onSave) {
        await onSave(_id)
      }

      toast.success(isSaved ? 'Post removed from bookmarks' : 'Post saved to bookmarks')
    } catch (error) {
      // Revert optimistic update on error
      setIsSaved(isSaved)
      toast.error('Failed to save post')
    }
  }

  // Handle delete post
  const handleDeletePost = async (e) => {
    e.preventDefault()
    e.stopPropagation()
    
    if (isDeleting) return;
    
    try {
      setIsDeleting(true);
      await dispatch(deletePost(_id)).unwrap();
      toast.success('Post deleted successfully');
      navigate('/blog');
    } catch (error) {
      console.error('Error deleting post:', error);
      toast.error('Failed to delete post');
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  // Render compact variant
  if (variant === 'compact') {
    return (
      <motion.article
        className={`group bg-white dark:bg-secondary-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-secondary-200 dark:border-secondary-700 ${className}`}
        whileHover={{ y: -4, scale: 1.02 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        {/* Featured Image */}
        {featuredImage && (
          <div className="relative overflow-hidden h-48">
            <Link to={`/blog/${slug}`}>
              <img
                src={featuredImage}
                alt={title}
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Link>
            {status === 'draft' && (
              <div className="absolute top-3 left-3 px-2 py-1 bg-yellow-500 text-white text-xs font-medium rounded-full">
                Draft
              </div>
            )}
            {category && (
              <div className="absolute top-3 right-3">
                <span className="inline-block px-3 py-1 text-xs font-medium text-white bg-primary-500/90 backdrop-blur-sm rounded-full">
                  {category}
                </span>
              </div>
            )}
          </div>
        )}

        <div className="p-6">
          {/* Category (if no image) */}
          {!featuredImage && category && (
            <div className="mb-3">
              <span className="inline-block px-3 py-1 text-xs font-medium text-primary-600 bg-primary-50 dark:bg-primary-900/20 dark:text-primary-400 rounded-full">
                {category}
              </span>
            </div>
          )}

          {/* Title */}
          <h3 className="text-xl font-bold text-secondary-900 dark:text-white mb-3 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200">
            <Link
              to={`/blog/${slug}`}
              className="hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
            >
              {title}
            </Link>
          </h3>

          {/* Excerpt */}
          {excerpt && (
            <p className="text-secondary-600 dark:text-secondary-400 text-sm line-clamp-2 mb-4 leading-relaxed">
              {excerpt}
            </p>
          )}

          {/* Author and Date */}
          <div className="flex items-center justify-between text-sm">
            {showAuthor && author && (
              <div className="flex items-center space-x-2">
                <img
                  src={author.avatar || `https://ui-avatars.com/api/?name=${author.firstName}+${author.lastName}&background=3b82f6&color=fff`}
                  alt={`${author.firstName} ${author.lastName}`}
                  className="w-8 h-8 rounded-full ring-2 ring-white dark:ring-secondary-700"
                />
                <div>
                  <span className="font-medium text-secondary-900 dark:text-white">{author.firstName} {author.lastName}</span>
                  {publishedAt && (
                    <div className="text-secondary-500 dark:text-secondary-400 text-xs">
                      {formatDate(publishedAt, DATE_FORMATS.SHORT)}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Stats */}
            <div className="flex items-center space-x-3 text-secondary-500 dark:text-secondary-400">
              {views && (
                <div className="flex items-center space-x-1">
                  <HiEye className="w-4 h-4" />
                  <span className="text-xs">{formatNumber(views)}</span>
                </div>
              )}
              <div className="flex items-center space-x-1">
                <HiHeart className="w-4 h-4" />
                <span className="text-xs">{formatNumber(likeCount)}</span>
              </div>
            </div>
          </div>
          
          {/* Author actions - Only show for the post author */}
          {isAuthor && (
            <div className="flex items-center justify-end mt-3 space-x-2">
              <Link to={`/edit-post/${_id}`} onClick={(e) => e.stopPropagation()}>
                <button 
                  className="px-2 py-1 text-xs flex items-center bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 rounded hover:bg-blue-200 dark:hover:bg-blue-800/50"
                >
                  <HiPencil className="w-3 h-3 mr-1" />
                  Edit
                </button>
              </Link>
              <button 
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  if(window.confirm(`Are you sure you want to delete "${title}"?`)) {
                    handleDeletePost(e);
                  }
                }}
                className="px-2 py-1 text-xs flex items-center bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 rounded hover:bg-red-200 dark:hover:bg-red-800/50"
                disabled={isDeleting}
              >
                <HiTrash className="w-3 h-3 mr-1" />
                {isDeleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          )}
        </div>
      </motion.article>
    )
  }

  // Default and featured variants
  const isFeatureVariant = variant === 'featured'

  return (
    <motion.article
      className={`group bg-white dark:bg-secondary-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-secondary-200 dark:border-secondary-700 ${className}`}
      whileHover={{ y: -4, scale: 1.01 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className={`flex ${isFeatureVariant ? 'flex-col' : 'flex-col md:flex-row'}`}>
        {/* Enhanced Featured Image */}
        {featuredImage && (
          <div className={isFeatureVariant ? 'w-full' : 'md:w-1/3 lg:w-1/4'}>
            <Link to={`/blog/${slug}`}>
              <div className="relative overflow-hidden">
                <img
                  src={featuredImage}
                  alt={title}
                  className={`w-full object-cover group-hover:scale-110 transition-transform duration-500 ${
                    isFeatureVariant ? 'h-64' : 'h-48 md:h-full'
                  }`}
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                {status === 'draft' && (
                  <div className="absolute top-3 left-3">
                    <span className="px-3 py-1 text-xs font-medium bg-yellow-500 text-white rounded-full shadow-lg">
                      Draft
                    </span>
                  </div>
                )}
                {category && (
                  <div className="absolute top-3 right-3">
                    <span className="px-3 py-1 text-xs font-medium text-white bg-primary-500/90 backdrop-blur-sm rounded-full shadow-lg">
                      {category}
                    </span>
                  </div>
                )}
                {/* Reading time overlay */}
                <div className="absolute bottom-3 left-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <span className="px-2 py-1 text-xs font-medium text-white bg-black/50 backdrop-blur-sm rounded-full">
                    {readingTime || '5 min read'}
                  </span>
                </div>
              </div>
            </Link>
          </div>
        )}

        {/* Enhanced Content */}
        <div className={`flex-1 p-8 ${featuredImage ? '' : 'md:p-10'}`}>
          {/* Category and Tags (if no image) */}
          {!featuredImage && (
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                {category && (
                  <span className="inline-block px-3 py-1 text-xs font-medium text-primary-600 bg-primary-50 dark:bg-primary-900/20 dark:text-primary-400 rounded-full">
                    {category}
                  </span>
                )}
                {tags.length > 0 && (
                  <div className="flex items-center space-x-1">
                    <HiTag className="w-4 h-4 text-secondary-400" />
                    <span className="text-sm text-secondary-500 dark:text-secondary-400">
                      {tags.slice(0, 2).join(', ')}
                      {tags.length > 2 && ` +${tags.length - 2}`}
                    </span>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Title */}
          <h3 className={`font-bold text-secondary-900 dark:text-white mb-4 line-clamp-2 group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors duration-200 ${
            isFeatureVariant ? 'text-2xl' : 'text-xl md:text-2xl'
          }`}>
            <Link
              to={`/blog/${slug}`}
              className="hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
            >
              {title}
            </Link>
          </h3>

          {/* Excerpt */}
          {excerpt && (
            <p className="text-secondary-600 dark:text-secondary-400 mb-6 line-clamp-3 leading-relaxed">
              {excerpt}
            </p>
          )}

          {/* Enhanced Action buttons */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {showActions && isAuthenticated && (
                <>
                  <button
                    onClick={handleLike}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                      isLiked
                        ? 'bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400'
                        : 'hover:bg-secondary-100 dark:hover:bg-secondary-700 text-secondary-600 dark:text-secondary-400'
                    }`}
                    title={isLiked ? 'Unlike post' : 'Like post'}
                  >
                    {isLiked ? (
                      <HiHeart className="w-4 h-4" />
                    ) : (
                      <HiOutlineHeart className="w-4 h-4" />
                    )}
                    <span className="text-sm font-medium">{formatNumber(likeCount)}</span>
                  </button>
                  <button
                    onClick={handleSave}
                    className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                      isSaved
                        ? 'bg-primary-50 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400'
                        : 'hover:bg-secondary-100 dark:hover:bg-secondary-700 text-secondary-600 dark:text-secondary-400'
                    }`}
                    title={isSaved ? 'Remove from bookmarks' : 'Save to bookmarks'}
                  >
                    {isSaved ? (
                      <HiBookmark className="w-4 h-4" />
                    ) : (
                      <HiOutlineBookmark className="w-4 h-4" />
                    )}
                    <span className="text-sm font-medium">Save</span>
                  </button>
                </>
              )}
              
              {/* Author actions - Only show for the post author */}
              {isAuthor && (
                <>
                  <Link to={`/edit-post/${_id}`} onClick={(e) => e.stopPropagation()}>
                    <button 
                      className="px-2 py-1 flex items-center bg-blue-100 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400 rounded hover:bg-blue-200 dark:hover:bg-blue-800/50"
                      title="Edit post"
                    >
                      <HiPencil className="w-4 h-4 mr-1" />
                      Edit
                    </button>
                  </Link>
                  <button 
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      if(window.confirm(`Are you sure you want to delete "${title}"?`)) {
                        handleDeletePost(e);
                      }
                    }}
                    className="px-2 py-1 flex items-center bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400 rounded hover:bg-red-200 dark:hover:bg-red-800/50"
                    title="Delete post"
                    disabled={isDeleting}
                  >
                    <HiTrash className="w-4 h-4 mr-1" />
                    {isDeleting ? 'Deleting...' : 'Delete'}
                  </button>
                </>
              )}
            </div>
          </div>

          {/* Title */}
          <h2 className={`font-bold text-secondary-900 dark:text-white mb-3 line-clamp-2 ${
            isFeatureVariant ? 'text-2xl md:text-3xl' : 'text-xl md:text-2xl'
          }`}>
            <Link
              to={`/blog/${slug}`}
              className="hover:text-primary-600 dark:hover:text-primary-400 transition-colors"
            >
              {title}
            </Link>
          </h2>

          {/* Excerpt */}
          {excerpt && (
            <p className="text-secondary-600 dark:text-secondary-300 mb-4 line-clamp-3">
              {truncateText(excerpt, isFeatureVariant ? 200 : 150)}
            </p>
          )}

          {/* Author Info */}
          {showAuthor && author && (
            <div className="flex items-center mb-4">
              <Link to={`/profile/${author.username}`} className="flex items-center group">
                <img
                  src={author.avatar || `https://ui-avatars.com/api/?name=${author.firstName}+${author.lastName}&background=3b82f6&color=fff`}
                  alt={`${author.firstName} ${author.lastName}`}
                  className="w-8 h-8 rounded-full mr-3"
                />
                <div>
                  <p className="text-sm font-medium text-secondary-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                    {author.firstName} {author.lastName}
                  </p>
                  <p className="text-xs text-secondary-500 dark:text-secondary-400">
                    @{author.username}
                  </p>
                </div>
              </Link>
            </div>
          )}

          {/* Meta Info */}
          <div className="flex items-center justify-between text-sm text-secondary-500 dark:text-secondary-400">
            <div className="flex items-center space-x-4">
              {publishedAt && (
                <div className="flex items-center">
                  <HiCalendar className="w-4 h-4 mr-1" />
                  <span title={formatDate(publishedAt, DATE_FORMATS.EXACT)}>{formatDate(publishedAt, DATE_FORMATS.SHORT)}</span>
                </div>
              )}

              {readTime && (
                <div className="flex items-center">
                  <HiClock className="w-4 h-4 mr-1" />
                  <span>{readTime} min read</span>
                </div>
              )}
            </div>

            <div className="flex items-center space-x-4">
              {views !== undefined && (
                <div className="flex items-center">
                  <HiEye className="w-4 h-4 mr-1" />
                  <span>{formatNumber(views)}</span>
                </div>
              )}

              <div className="flex items-center">
                <HiHeart className="w-4 h-4 mr-1" />
                <span>{formatNumber(likeCount)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.article>
  )
}

export default PostCard
