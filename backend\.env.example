# Server Configuration
NODE_ENV=development
PORT=5000

# Database Configuration
MONGODB_URI=Your-MongoDB-Connection-String-OR-MongoDB-Atlas-Connection-String

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=15m
ROTATE_REFRESH_TOKENS=true

# Session Configuration
SESSION_SECRET=your-super-secret-session-key

CLOUDINARY_CLOUD_NAME=claud-name
CLOUDINARY_API_KEY=api-key
CLOUDINARY_API_SECRET=api-secret

# Client Configuration
CLIENT_URL=http://localhost:5173

# Google OAuth Configuration
GOOGLE_CLIENT_ID=client-id-googleAuth
GOOGLE_CLIENT_SECRET=client-secret-googleAuth
GOOGLE_CALLBACK_URL=http://localhost:5000/api/auth/google/callback
VITE_ENABLE_SOCIAL_LOGIN=true

# Rate Limiting Configuration (Development)
DISABLE_RATE_LIMIT=false
DISABLE_AUTH_RATE_LIMIT=false
