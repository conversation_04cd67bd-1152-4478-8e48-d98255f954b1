import { useState, useEffect, useCallback } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { useSearchParams, useNavigate } from 'react-router-dom'
import { Helmet } from 'react-helmet-async'
import { motion, AnimatePresence } from 'framer-motion'
import { Hi<PERSON>iew<PERSON>rid, Hi<PERSON>iewList, Hi<PERSON>rendingUp, <PERSON><PERSON><PERSON>, Hi<PERSON><PERSON> } from 'react-icons/hi'
import toast from 'react-hot-toast'

// Redux actions
import { fetchPosts, likePost, setFilters, clearFilters } from '../store/slices/postsSlice'

// Components
import PostCard from '../components/ui/PostCard'
import BlogFilters from '../components/blog/BlogFilters'
import Pagination from '../components/ui/Pagination'
import PageTransition from '../components/ui/PageTransition'
import {
  BlogListSkeleton,
  BlogGridSkeleton,
  BlogFiltersSkeleton,
  FeaturedPostSkeleton
} from '../components/blog/BlogSkeleton'
import {
  BlogErrorState,
  BlogEmptyState,
  BlogSearchEmptyState
} from '../components/blog/BlogStates'

// Services
import postsService from '../services/postsService'

// Utils
import { useProgressAction } from '../hooks/useProgressAction'

const BlogList = () => {
  const dispatch = useDispatch()
  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const { executeWithProgress } = useProgressAction()

  // Redux state
  const {
    posts,
    loading,
    error,
    pagination,
    filters
  } = useSelector((state) => state.posts)
  const { isAuthenticated } = useSelector((state) => state.auth)

  // Local state
  const [viewMode, setViewMode] = useState('list') // 'list' or 'grid'
  const [featuredPosts, setFeaturedPosts] = useState([])
  const [popularPosts, setPopularPosts] = useState([])
  const [categories, setCategories] = useState([])
  const [tags, setTags] = useState([])
  const [loadingFeatured, setLoadingFeatured] = useState(true)
  const [showFeatured, setShowFeatured] = useState(true)

  // Initialize filters from URL params
  useEffect(() => {
    const urlFilters = {
      search: searchParams.get('search') || '',
      category: searchParams.get('category') || '',
      tag: searchParams.get('tag') || '',
      sortBy: searchParams.get('sort') || 'newest',
      page: parseInt(searchParams.get('page')) || 1
    }

    dispatch(setFilters(urlFilters))
  }, [searchParams, dispatch])

  // Fetch posts when filters change
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Don't use executeWithProgress here as the API already has progress tracking
        await dispatch(fetchPosts({
          page: filters.page || 1,
          limit: 10,
          search: filters.search,
          category: filters.category,
          tag: filters.tag,
          sortBy: filters.sortBy
        })).unwrap()
      } catch (error) {
        console.error('Error fetching posts:', error)
      }
    }

    fetchData()
  }, [dispatch, filters])

  // Fetch additional data on component mount
  useEffect(() => {
    const fetchAdditionalData = async () => {
      try {
        setLoadingFeatured(true)

        // Add silent flag to prevent progress indicator conflicts
        const options = { silent: true };

        // Fetch featured posts, categories, and tags in parallel
        const [featuredResponse, categoriesResponse, tagsResponse] = await Promise.all([
          postsService.getFeaturedPosts(3, options),
          postsService.getCategories?.(options) || Promise.resolve([]),
          postsService.getTags?.(options) || Promise.resolve([])
        ])

        setFeaturedPosts(featuredResponse?.posts || [])
        // Extract category names from API response objects
        setCategories(categoriesResponse?.map(cat => cat.category) || [])
        // Extract tag names from API response objects
        setTags(tagsResponse?.map(tag => tag.tag) || [])
      } catch (error) {
        console.error('Error fetching additional data:', error)
      } finally {
        setLoadingFeatured(false)
      }
    }

    fetchAdditionalData()
  }, [])

  // Update URL when filters change
  const updateURL = useCallback((newFilters) => {
    const params = new URLSearchParams()

    Object.entries(newFilters).forEach(([key, value]) => {
      if (value && value !== '' && key !== 'page') {
        params.set(key === 'sortBy' ? 'sort' : key, value)
      }
    })

    if (newFilters.page && newFilters.page > 1) {
      params.set('page', newFilters.page)
    }

    setSearchParams(params)
  }, [setSearchParams])

  // Handle filter changes
  const handleFiltersChange = useCallback((newFilters) => {
    const updatedFilters = { ...newFilters, page: 1 } // Reset to first page
    dispatch(setFilters(updatedFilters))
    updateURL(updatedFilters)
  }, [dispatch, updateURL])

  // Handle page changes
  const handlePageChange = useCallback((page) => {
    const updatedFilters = { ...filters, page }
    dispatch(setFilters(updatedFilters))
    updateURL(updatedFilters)

    // Scroll to top
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }, [filters, dispatch, updateURL])

  // Handle post like
  const handlePostLike = useCallback(async (postId) => {
    if (!isAuthenticated) {
      toast.error('Please log in to like posts')
      return
    }

    try {
      await dispatch(likePost(postId)).unwrap()
    } catch (error) {
      console.error('Error liking post:', error)
      throw error
    }
  }, [dispatch, isAuthenticated])

  // Handle post save
  const handlePostSave = useCallback(async (postId) => {
    if (!isAuthenticated) {
      toast.error('Please log in to save posts')
      return
    }

    try {
      await postsService.toggleSavePost(postId)
    } catch (error) {
      console.error('Error saving post:', error)
      throw error
    }
  }, [isAuthenticated])

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    dispatch(clearFilters())
    setSearchParams({})
  }, [dispatch, setSearchParams])

  // Check if we have active filters
  const hasActiveFilters = filters.search || filters.category || filters.tag || filters.sortBy !== 'newest'
  const hasSearchQuery = filters.search && filters.search.trim().length > 0

  return (
    <PageTransition>
      <Helmet>
        <title>
          {hasSearchQuery
            ? `Search: ${filters.search} - Blog Posts`
            : filters.category
            ? `${filters.category} - Blog Posts`
            : 'Blog Posts - MERN Blog Platform'
          }
        </title>
        <meta
          name="description"
          content={
            hasSearchQuery
              ? `Search results for "${filters.search}" on our blog platform`
              : filters.category
              ? `Explore ${filters.category} blog posts on our platform`
              : "Explore all blog posts on our platform. Discover articles on technology, lifestyle, travel, and more."
          }
        />
      </Helmet>

      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-br from-primary-600 via-primary-700 to-accent-600 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-primary-600/90 to-accent-600/90"></div>

        {/* Decorative elements */}
        <div className="absolute top-0 left-0 w-full h-full overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-accent-400/20 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-primary-400/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative container-custom py-16 md:py-20">
          <div className="text-center">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="mb-6"
            >
              <span className="inline-block px-4 py-2 bg-white/20 backdrop-blur-sm rounded-full text-sm font-medium mb-4">
                ✨ Welcome to our Blog
              </span>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                {hasSearchQuery
                  ? `Search Results for "${filters.search}"`
                  : filters.category
                  ? `${filters.category} Posts`
                  : (
                    <>
                      Discover Amazing
                      <span className="block bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                        Stories
                      </span>
                    </>
                  )
                }
              </h1>
            </motion.div>

            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-lg md:text-xl text-white/90 max-w-2xl mx-auto mb-8 leading-relaxed"
            >
              {hasSearchQuery
                ? `Found ${pagination.totalPosts} results matching your search`
                : filters.category
                ? `Discover the latest ${filters.category.toLowerCase()} articles from our community`
                : 'Explore our collection of insightful articles, tutorials, and stories from passionate writers around the world.'
              }
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 justify-center items-center"
            >
              <div className="flex items-center space-x-6 text-white/80">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium">{pagination.totalPosts || 0} Articles</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium">{categories.length} Categories</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium">Daily Updates</span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      <div className="min-h-screen bg-gradient-to-br from-secondary-50 via-white to-primary-50 dark:from-secondary-900 dark:via-secondary-900 dark:to-secondary-800">
        <div className="container-custom py-12">
        {/* Enhanced Header with Controls */}
        <div className="mb-8">
          <div className="bg-white dark:bg-secondary-800 rounded-2xl shadow-lg border border-secondary-200 dark:border-secondary-700 p-6 mb-8">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
              <div className="flex-1">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-gradient-to-r from-primary-500 to-accent-500 rounded-full"></div>
                    <span className="text-sm font-medium text-secondary-600 dark:text-secondary-400 uppercase tracking-wide">
                      {hasSearchQuery ? 'Search Results' : filters.category ? 'Category' : 'All Posts'}
                    </span>
                  </div>
                  {!loading && posts.length > 0 && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800 dark:bg-primary-900/20 dark:text-primary-300">
                      {pagination.totalPosts} posts
                    </span>
                  )}
                </div>

                <div className="flex flex-wrap items-center gap-4 text-sm text-secondary-600 dark:text-secondary-400">
                  <div className="flex items-center space-x-1">
                    <HiTrendingUp className="w-4 h-4 text-green-500" />
                    <span>Trending</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <HiClock className="w-4 h-4 text-blue-500" />
                    <span>Updated daily</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <HiEye className="w-4 h-4 text-purple-500" />
                    <span>Fresh content</span>
                  </div>
                </div>
              </div>

              {/* Enhanced View Mode Toggle */}
              <div className="flex items-center space-x-4 mt-6 lg:mt-0">
                <span className="text-sm font-medium text-secondary-700 dark:text-secondary-300">View:</span>
                <div className="flex bg-secondary-100 dark:bg-secondary-700 rounded-xl p-1 shadow-inner">
                  <button
                    onClick={() => setViewMode('list')}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                      viewMode === 'list'
                        ? 'bg-white dark:bg-secondary-600 text-primary-600 shadow-sm transform scale-105'
                        : 'text-secondary-600 dark:text-secondary-400 hover:text-secondary-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-secondary-600/50'
                    }`}
                    title="List view"
                  >
                    <HiViewList className="w-4 h-4" />
                    <span className="text-sm font-medium">List</span>
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                      viewMode === 'grid'
                        ? 'bg-white dark:bg-secondary-600 text-primary-600 shadow-sm transform scale-105'
                        : 'text-secondary-600 dark:text-secondary-400 hover:text-secondary-900 dark:hover:text-white hover:bg-white/50 dark:hover:bg-secondary-600/50'
                    }`}
                    title="Grid view"
                  >
                    <HiViewGrid className="w-4 h-4" />
                    <span className="text-sm font-medium">Grid</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Featured Posts Section */}
        {showFeatured && !hasActiveFilters && (
          <div className="mb-16">
            <div className="bg-white dark:bg-secondary-800 rounded-2xl shadow-lg border border-secondary-200 dark:border-secondary-700 p-8">
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl">
                    <span className="text-white text-lg">⭐</span>
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-secondary-900 dark:text-white">
                      Featured Posts
                    </h2>
                    <p className="text-sm text-secondary-600 dark:text-secondary-400">
                      Handpicked articles from our community
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowFeatured(false)}
                  className="inline-flex items-center space-x-2 px-4 py-2 bg-secondary-100 dark:bg-secondary-700 text-secondary-600 dark:text-secondary-400 rounded-lg hover:bg-secondary-200 dark:hover:bg-secondary-600 transition-colors duration-200 text-sm font-medium"
                >
                  <span>Hide Featured</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

            {loadingFeatured ? (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {Array.from({ length: 3 }).map((_, index) => (
                  <FeaturedPostSkeleton key={index} />
                ))}
              </div>
            ) : featuredPosts.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {featuredPosts.map((post, index) => (
                  <motion.div
                    key={post._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <PostCard
                      post={post}
                      variant="featured"
                      showActions={true}
                      onLike={handlePostLike}
                      onSave={handlePostSave}
                    />
                  </motion.div>
                ))}
              </div>
            ) : null}
          </div>
        )}

        {/* Filters */}
        {loading && !posts.length ? (
          <BlogFiltersSkeleton className="mb-8" />
        ) : (
          <BlogFilters
            onFiltersChange={handleFiltersChange}
            initialFilters={filters}
            categories={categories}
            tags={tags}
            className="mb-8"
          />
        )}

        {/* Main Content */}
        <div className="space-y-8">
          {/* Loading State */}
          {loading && !posts.length && (
            <>
              {viewMode === 'list' ? (
                <BlogListSkeleton count={6} />
              ) : (
                <BlogGridSkeleton count={9} />
              )}
            </>
          )}

          {/* Error State */}
          {error && !loading && (
            <BlogErrorState
              title="Failed to load posts"
              message={error}
              onRetry={() => dispatch(fetchPosts(filters))}
            />
          )}

          {/* Empty States */}
          {!loading && !error && posts.length === 0 && (
            <>
              {hasSearchQuery ? (
                <BlogSearchEmptyState
                  searchQuery={filters.search}
                  onClearSearch={handleClearFilters}
                />
              ) : hasActiveFilters ? (
                <BlogEmptyState
                  title="No posts match your filters"
                  message="Try adjusting your search criteria or clear the filters to see all posts."
                  showSearchSuggestion={true}
                  onClearFilters={handleClearFilters}
                />
              ) : (
                <BlogEmptyState
                  title="No blog posts yet"
                  message="Be the first to share your thoughts with the community!"
                  showCreateButton={isAuthenticated}
                />
              )}
            </>
          )}

          {/* Enhanced Posts List/Grid */}
          {!loading && !error && posts.length > 0 && (
            <div className="bg-white dark:bg-secondary-800 rounded-2xl shadow-lg border border-secondary-200 dark:border-secondary-700 p-8">
              <div className="flex items-center justify-between mb-8">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-8 bg-gradient-to-b from-primary-500 to-accent-500 rounded-full"></div>
                  <h2 className="text-2xl font-bold text-secondary-900 dark:text-white">
                    {hasSearchQuery ? 'Search Results' : filters.category ? `${filters.category} Articles` : 'Latest Articles'}
                  </h2>
                </div>
                <div className="text-sm text-secondary-600 dark:text-secondary-400">
                  Showing {posts.length} of {pagination.totalPosts} posts
                </div>
              </div>

              <AnimatePresence mode="wait">
                {viewMode === 'list' ? (
                  <motion.div
                    key="list-view"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-8"
                  >
                    {posts.map((post, index) => (
                      <motion.div
                        key={post._id}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                      >
                        <PostCard
                          post={post}
                          variant="default"
                          showActions={true}
                          onLike={handlePostLike}
                          onSave={handlePostSave}
                        />
                      </motion.div>
                    ))}
                  </motion.div>
                ) : (
                  <motion.div
                    key="grid-view"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
                  >
                    {posts.map((post, index) => (
                      <motion.div
                        key={post._id}
                        initial={{ opacity: 0, scale: 0.9, y: 20 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: index * 0.1 }}
                      >
                        <PostCard
                          post={post}
                          variant="compact"
                          showActions={true}
                          onLike={handlePostLike}
                          onSave={handlePostSave}
                        />
                      </motion.div>
                    ))}
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          )}

          {/* Enhanced Pagination */}
          {!loading && !error && posts.length > 0 && pagination.totalPages > 1 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
              className="mt-12"
            >
              <div className="bg-white dark:bg-secondary-800 rounded-2xl shadow-lg border border-secondary-200 dark:border-secondary-700 p-6">
                <div className="flex flex-col items-center space-y-4">
                  <div className="text-center">
                    <p className="text-sm text-secondary-600 dark:text-secondary-400">
                      Showing <span className="font-medium text-secondary-900 dark:text-white">{((pagination.currentPage - 1) * 10) + 1}</span> to{' '}
                      <span className="font-medium text-secondary-900 dark:text-white">
                        {Math.min(pagination.currentPage * 10, pagination.totalPosts)}
                      </span> of{' '}
                      <span className="font-medium text-secondary-900 dark:text-white">{pagination.totalPosts}</span> results
                    </p>
                  </div>
                  <Pagination
                    currentPage={pagination.currentPage}
                    totalPages={pagination.totalPages}
                    totalItems={pagination.totalPosts}
                    itemsPerPage={10}
                    onPageChange={handlePageChange}
                    showInfo={false}
                    showFirstLast={true}
                    maxVisiblePages={5}
                  />
                </div>
              </div>
            </motion.div>
          )}

          {/* Enhanced Loading More Indicator */}
          {loading && posts.length > 0 && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="flex justify-center py-12"
            >
              <div className="bg-white dark:bg-secondary-800 rounded-2xl shadow-lg border border-secondary-200 dark:border-secondary-700 px-8 py-6">
                <div className="flex items-center space-x-3 text-secondary-600 dark:text-secondary-400">
                  <div className="w-6 h-6 border-3 border-primary-500 border-t-transparent rounded-full animate-spin"></div>
                  <span className="font-medium">Loading more amazing posts...</span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Bottom spacing */}
          <div className="h-8"></div>
        </div>
      </div>
    </PageTransition>
  )
}

export default BlogList
