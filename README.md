# Blogging Platform

A full-stack blogging platform with a React frontend and Node.js backend.

## Features

- User authentication and authorization
- Create, read, update, and delete blog posts
- Comment system with real-time updates
- Admin dashboard for content moderation
- Rich text editor for post creation
- Responsive design with Tailwind CSS
- File uploads with Cloudinary integration

## Project Structure

- **Frontend**: React with Vite, Redux, Tailwind CSS
- **Backend**: Node.js, Express, MongoDB (Mongoose)

## Getting Started

### Prerequisites

- Node.js
- npm or yarn
- MongoDB

### Installation

1. Clone the repository
2. Install backend dependencies:
   ```
   cd backend
   npm install
   ```
3. Install frontend dependencies:
   ```
   cd frontend
   npm install
   ```

### Running the Application

1. Start the backend server:
   ```
   cd backend
   npm start
   ```
2. Start the frontend development server:
   ```
   cd frontend
   npm run dev
   ```

## License

MIT 