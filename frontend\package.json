{"name": "mern-blog-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"@reduxjs/toolkit": "^1.9.7", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "dompurify": "^3.2.6", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-icons": "^4.12.0", "react-intersection-observer": "^9.5.3", "react-markdown": "^9.0.1", "react-quill": "^2.0.0", "react-redux": "^8.1.3", "react-router-dom": "^6.20.1", "react-syntax-highlighter": "^15.5.0", "redux-persist": "^6.0.0", "remark-gfm": "^4.0.0", "socket.io-client": "^4.7.2"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "jsdom": "^23.0.1", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "vite": "^4.5.0", "vitest": "^0.34.6"}}